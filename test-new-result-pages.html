<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New Result Pages</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            background: #4F46E5;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #4338CA;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info {
            background: #EEF2FF;
            border: 1px solid #C7D2FE;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .success {
            background: #F0FDF4;
            border: 1px solid #BBF7D0;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test New Result Pages</h1>
        
        <div class="info">
            <h3>📋 Test Setup</h3>
            <p>Halaman ini akan mengatur data test untuk menguji flow hasil assessment yang baru:</p>
            <ul>
                <li><strong>Brief Result</strong> - Ringkasan hasil dengan progress journey</li>
                <li><strong>VIA-IS Page</strong> - Character Strengths dengan visualisasi 24 kekuatan</li>
                <li><strong>RIASEC Page</strong> - Holland Codes dengan radar chart</li>
                <li><strong>OCEAN Page</strong> - Big Five Personality dengan detail analisis</li>
                <li><strong>Persona Profile Page</strong> - Profil persona lengkap</li>
            </ul>
            <p><strong>Flow:</strong> Brief Result → VIA-IS → RIASEC → OCEAN → Persona → Brief Result</p>
        </div>

        <div class="button-grid">
            <button class="button" onclick="setupTestData()">
                🔧 Setup Test Data
            </button>
            <button class="button" onclick="openBriefResult()">
                🚀 Open Brief Result
            </button>
            <button class="button" onclick="testViaIsPage()">
                💎 Test VIA-IS Page
            </button>
            <button class="button" onclick="testRiasecPage()">
                🎯 Test RIASEC Page
            </button>
            <button class="button" onclick="testOceanPage()">
                🌊 Test OCEAN Page
            </button>
            <button class="button" onclick="testPersonaPage()">
                🧠 Test Persona Page
            </button>
        </div>

        <div id="status" class="info" style="display: none;">
            <h3>Status</h3>
            <p id="statusMessage">Ready to test...</p>
        </div>
    </div>

    <script>
        // Sample assessment results data
        const sampleAssessmentResults = {
            ocean: {
                openness: 85,
                conscientiousness: 78,
                extraversion: 45,
                agreeableness: 62,
                neuroticism: 35
            },
            riasec: {
                realistic: 25,
                investigative: 92,
                artistic: 78,
                social: 45,
                enterprising: 55,
                conventional: 68
            },
            viaIs: {
                creativity: 88,
                curiosity: 92,
                judgment: 85,
                loveOfLearning: 90,
                perspective: 82,
                bravery: 65,
                perseverance: 78,
                honesty: 75,
                zest: 58,
                love: 62,
                kindness: 68,
                socialIntelligence: 55,
                teamwork: 48,
                fairness: 72,
                leadership: 65,
                forgiveness: 58,
                humility: 62,
                prudence: 85,
                selfRegulation: 75,
                appreciationOfBeauty: 82,
                gratitude: 68,
                hope: 72,
                humor: 55,
                spirituality: 45
            }
        };

        // Sample persona profile data
        const samplePersonaProfile = {
            archetype: "The Analytical Innovator",
            shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
            strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
            strengths: [
                "Kemampuan analisis yang tajam",
                "Kreativitas dan inovasi",
                "Keingintahuan intelektual yang tinggi",
                "Kemampuan belajar mandiri yang kuat",
                "Pemikiran sistematis dan terstruktur"
            ],
            weaknessSummary: "Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat atau bekerja sama dengan orang lain.",
            weaknesses: [
                "Terkadang terlalu perfeksionis",
                "Dapat terjebak dalam overthinking",
                "Kurang sabar dengan proses yang lambat",
                "Kemampuan sosial yang perlu dikembangkan",
                "Kesulitan mendelegasikan tugas"
            ],
            careerRecommendation: [
                {
                    careerName: "Data Scientist",
                    careerProspect: {
                        jobAvailability: "high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                },
                {
                    careerName: "Peneliti",
                    careerProspect: {
                        jobAvailability: "moderate",
                        salaryPotential: "moderate",
                        careerProgression: "moderate",
                        industryGrowth: "moderate",
                        skillDevelopment: "high"
                    }
                },
                {
                    careerName: "Pengembang Software",
                    careerProspect: {
                        jobAvailability: "super high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                }
            ],
            insights: [
                "Kembangkan keterampilan komunikasi untuk menyampaikan ide kompleks dengan lebih efektif",
                "Latih kemampuan bekerja dalam tim untuk mengimbangi kecenderungan bekerja sendiri",
                "Manfaatkan kekuatan analitis untuk memecahkan masalah sosial",
                "Cari mentor yang dapat membantu mengembangkan keterampilan kepemimpinan",
                "Tetapkan batas waktu untuk menghindari analisis berlebihan"
            ],
            skillSuggestion: [
                "Public Speaking",
                "Leadership",
                "Teamwork",
                "Time Management",
                "Delegation"
            ],
            possiblePitfalls: [
                "Mengisolasi diri dari tim karena terlalu fokus pada analisis individu",
                "Menunda keputusan karena perfeksionisme berlebihan",
                "Kurang membangun jaringan karena terlalu fokus pada teknis"
            ],
            riskTolerance: "moderate",
            workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
            roleModel: [
                "Marie Curie",
                "Albert Einstein",
                "B.J. Habibie"
            ]
        };

        function showStatus(message, isSuccess = false) {
            const statusDiv = document.getElementById('status');
            const statusMessage = document.getElementById('statusMessage');
            
            statusDiv.style.display = 'block';
            statusMessage.textContent = message;
            
            if (isSuccess) {
                statusDiv.className = 'info success';
            } else {
                statusDiv.className = 'info';
            }
        }

        function setupTestData() {
            try {
                // Save assessment results
                localStorage.setItem('assessmentResults', JSON.stringify(sampleAssessmentResults));
                
                // Save persona profile
                localStorage.setItem('personaProfile', JSON.stringify(samplePersonaProfile));
                
                // Set authentication data
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('userToken', 'dummy-token');
                localStorage.setItem('userData', JSON.stringify({
                    email: '<EMAIL>',
                    name: 'Test User'
                }));

                // Set assessment completion flags
                localStorage.setItem('assessmentCompleted', 'true');
                localStorage.setItem('assessmentResultReady', 'true');

                showStatus('✅ Test data berhasil disimpan! Anda sekarang dapat menguji semua halaman hasil assessment.', true);
            } catch (error) {
                showStatus('❌ Error: ' + error.message);
            }
        }

        function openBriefResult() {
            window.open('http://localhost:5173/#result', '_blank');
        }

        function testViaIsPage() {
            window.open('http://localhost:5173/#result-via-is', '_blank');
        }

        function testRiasecPage() {
            window.open('http://localhost:5173/#result-riasec', '_blank');
        }

        function testOceanPage() {
            window.open('http://localhost:5173/#result-ocean', '_blank');
        }

        function testPersonaPage() {
            window.open('http://localhost:5173/#result-persona', '_blank');
        }

        // Auto setup on page load
        window.addEventListener('load', function() {
            showStatus('Siap untuk setup test data. Klik "Setup Test Data" untuk memulai.');
        });
    </script>
</body>
</html>
