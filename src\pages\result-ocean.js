// Import utilities for OCEAN assessment details and charts
import { OCEAN_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultOceanPage() {
  return `
    <div class="min-h-screen light-theme">
      <!-- Premium Navigation -->
      <nav class="nav-glass sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-6">
              <button onclick="navigateTo('result-riasec')"
                class="flex items-center text-gray-600 hover:text-gray-900 transition-all duration-300 group">
                <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
                <span class="font-medium">RIASEC</span>
              </button>
              <div class="h-6 w-px bg-gray-300"></div>
              <h1 class="text-xl font-semibold text-gray-900">Personality Traits</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <span class="w-2 h-2 bg-emerald-500 rounded-full"></span>
                <span>Page 4 of 5</span>
              </div>
              <button onclick="navigateTo('result-persona')" class="btn-primary">
                Next: Persona Profile
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Modern Progress Indicator -->
      <div class="glass-card-light border-b-0 rounded-none">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex items-center justify-between mb-3">
            <span class="text-sm font-semibold text-gray-700">Assessment Progress</span>
            <span class="text-sm text-gray-600 font-medium">80% Complete</span>
          </div>
          <div class="progress-bar-modern">
            <div class="progress-fill-modern" style="width: 80%"></div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto py-12 px-6">
        <!-- Hero Section -->
        <div class="glass-card-light p-10 mb-12 relative overflow-hidden">
          <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 via-teal-500 to-green-500"></div>
          <div class="flex items-start space-x-8">
            <div class="flex-shrink-0">
              <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 via-teal-600 to-green-600 rounded-3xl flex items-center justify-center shadow-2xl animate-glow">
                <span class="text-3xl">🌊</span>
              </div>
            </div>
            <div class="flex-1">
              <h1 class="heading-secondary mb-4">OCEAN Big Five Personality Assessment</h1>
              <p class="text-premium text-lg leading-relaxed mb-6">
                The Big Five model measures 5 universal personality dimensions, providing a comprehensive view
                of your behavioral patterns, thinking styles, and emotional responses across various situations.
              </p>
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="font-medium">Scientifically Robust</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                  <span class="font-medium">Universally Accepted</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                  </svg>
                  <span class="font-medium">Evidence-Based</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- OCEAN Explanation -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Tentang OCEAN Big Five Personality</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Apa itu Big Five?</h4>
              <div class="space-y-4 text-gray-700">
                <p>
                  Model Big Five atau OCEAN adalah model kepribadian yang paling diterima secara ilmiah 
                  dan digunakan luas dalam psikologi kepribadian.
                </p>
                <p>
                  Model ini mengidentifikasi 5 dimensi utama kepribadian yang relatif stabil sepanjang hidup 
                  dan dapat memprediksi berbagai aspek perilaku, kinerja, dan kesejahteraan.
                </p>
                <p>
                  OCEAN merupakan singkatan dari: <strong>O</strong>penness, <strong>C</strong>onscientiousness, 
                  <strong>E</strong>xtraversion, <strong>A</strong>greeableness, dan <strong>N</strong>euroticism.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Manfaat Memahami Big Five</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Pemahaman diri yang mendalam dan akurat</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Prediksi kinerja kerja dan akademik</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan hubungan interpersonal</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Panduan untuk pengembangan diri</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Memahami gaya komunikasi dan kerja</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- OCEAN Dimensions Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">5 Dimensi Kepribadian OCEAN</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="ocean-dimensions-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- User's OCEAN Profile -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Profil OCEAN Anda</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Radar Chart -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Visualisasi Radar Chart</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="oceanRadarChart" width="400" height="400"></canvas>
              </div>
            </div>

            <!-- Personality Summary -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Ringkasan Kepribadian</h4>
              <div id="personality-summary" class="space-y-4">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed OCEAN Results -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Detail Hasil Assessment OCEAN</h3>
          <div id="ocean-detailed-results" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
          <button onclick="navigateTo('result-riasec')"
            class="bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            ← Kembali ke RIASEC
          </button>
          <button onclick="navigateTo('result-persona')"
            class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Lanjut ke Persona Profile →
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultOcean() {
  // Load OCEAN assessment data
  loadOceanData();
}

function loadOceanData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleOceanData();

    console.log('OCEAN assessment results loaded:', assessmentResults);

    displayOceanData(assessmentResults.ocean || assessmentResults);
  } catch (error) {
    console.error('Error loading OCEAN data:', error);
    // Fallback to sample data
    displayOceanData(getSampleOceanData());
  }
}

function getSampleOceanData() {
  return {
    openness: 85,
    conscientiousness: 78,
    extraversion: 45,
    agreeableness: 62,
    neuroticism: 35
  };
}

function displayOceanData(oceanData) {
  // Display dimensions overview
  displayDimensionsOverview();

  // Create radar chart
  createOceanRadarChart(oceanData);

  // Display personality summary
  displayPersonalitySummary(oceanData);

  // Display detailed results
  displayDetailedResults(oceanData);
}

function displayDimensionsOverview() {
  const container = document.getElementById('ocean-dimensions-overview');
  if (!container) return;

  const oceanDimensions = [
    {
      code: 'O',
      name: 'Openness',
      fullName: 'Openness to Experience',
      description: 'Keterbukaan terhadap pengalaman baru, kreativitas, dan ide-ide abstrak',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      icon: '🎨'
    },
    {
      code: 'C',
      name: 'Conscientiousness',
      fullName: 'Conscientiousness',
      description: 'Kedisiplinan, keteraturan, dan orientasi pada tujuan',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: '📋'
    },
    {
      code: 'E',
      name: 'Extraversion',
      fullName: 'Extraversion',
      description: 'Energi yang diarahkan keluar, sosiabilitas, dan assertiveness',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: '🎉'
    },
    {
      code: 'A',
      name: 'Agreeableness',
      fullName: 'Agreeableness',
      description: 'Kecenderungan untuk kooperatif, empati, dan kepercayaan',
      color: 'bg-green-50 border-green-200 text-green-800',
      icon: '🤝'
    },
    {
      code: 'N',
      name: 'Neuroticism',
      fullName: 'Neuroticism',
      description: 'Kecenderungan mengalami emosi negatif dan ketidakstabilan emosional',
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: '😰'
    }
  ];

  container.innerHTML = oceanDimensions.map(dimension => `
    <div class="border rounded-lg p-6 ${dimension.color}">
      <div class="flex items-center mb-4">
        <span class="text-3xl mr-4">${dimension.icon}</span>
        <div>
          <h4 class="font-semibold text-lg">${dimension.code} - ${dimension.name}</h4>
          <p class="text-xs opacity-75">${dimension.fullName}</p>
        </div>
      </div>
      <p class="text-sm">${dimension.description}</p>
    </div>
  `).join('');
}

function createOceanRadarChart(oceanData) {
  const ctx = document.getElementById('oceanRadarChart');
  if (!ctx) return;

  return ChartUtils.createOceanRadarChart(ctx, oceanData);
}

function displayPersonalitySummary(oceanData) {
  const container = document.getElementById('personality-summary');
  if (!container) return;

  // Sort dimensions by score
  const sortedDimensions = Object.entries(oceanData)
    .map(([key, value]) => ({
      key,
      value,
      details: OCEAN_DETAILS[key],
      name: OCEAN_DETAILS[key]?.name || key
    }))
    .sort((a, b) => b.value - a.value);

  const highest = sortedDimensions[0];
  const lowest = sortedDimensions[sortedDimensions.length - 1];

  container.innerHTML = `
    <div class="space-y-4">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h5 class="font-semibold text-green-800 mb-2">Dimensi Tertinggi</h5>
        <div class="flex justify-between items-center">
          <span class="text-green-700">${highest.name}</span>
          <span class="text-green-600 font-bold text-lg">${highest.value}%</span>
        </div>
        <p class="text-green-600 text-sm mt-2">${highest.details?.description || ''}</p>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h5 class="font-semibold text-orange-800 mb-2">Dimensi Terendah</h5>
        <div class="flex justify-between items-center">
          <span class="text-orange-700">${lowest.name}</span>
          <span class="text-orange-600 font-bold text-lg">${lowest.value}%</span>
        </div>
        <p class="text-orange-600 text-sm mt-2">${lowest.details?.description || ''}</p>
      </div>

      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 class="font-semibold text-blue-800 mb-2">Profil Kepribadian</h5>
        <div class="space-y-2">
          ${sortedDimensions.map(dim => `
            <div class="flex justify-between items-center">
              <span class="text-blue-700 text-sm">${dim.name}</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-blue-200 rounded-full h-2">
                  <div class="bg-blue-500 h-2 rounded-full" style="width: ${dim.value}%"></div>
                </div>
                <span class="text-blue-600 font-medium text-sm">${dim.value}%</span>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
}

function displayDetailedResults(oceanData) {
  const container = document.getElementById('ocean-detailed-results');
  if (!container) return;

  // Sort OCEAN scores to show highest first
  const sortedOcean = Object.entries(oceanData)
    .map(([key, value]) => ({ key, value, details: OCEAN_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedOcean.map(({ value, details }) => {
    const isHigh = value >= 60;
    const scoreData = isHigh ? details.highScore : details.lowScore;

    return `
      <div class="border rounded-lg p-4 ${value >= 70 ? 'border-green-300 bg-green-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
        <div class="flex justify-between items-center mb-3">
          <h4 class="font-semibold text-lg text-gray-900">${details.name}</h4>
          <div class="text-right">
            <div class="text-xl font-bold ${value >= 70 ? 'text-green-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
            <div class="text-xs ${value >= 70 ? 'text-green-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
              ${value >= 60 ? 'Tinggi' : value >= 40 ? 'Sedang' : 'Rendah'}
            </div>
          </div>
        </div>

        <p class="text-gray-700 text-sm mb-3">${details.description}</p>

        <div class="space-y-3">
          <div>
            <h5 class="font-medium text-gray-800 mb-2 text-sm">Karakteristik (${isHigh ? 'Tinggi' : 'Rendah'}):</h5>
            <ul class="text-gray-600 space-y-1 text-xs">
              ${scoreData.characteristics.slice(0, 3).map(char => `<li class="flex items-start"><span class="text-green-500 mr-1">•</span>${char}</li>`).join('')}
            </ul>
          </div>
          <div>
            <h5 class="font-medium text-gray-800 mb-2 text-sm">Implikasi:</h5>
            <ul class="text-gray-600 space-y-1 text-xs">
              ${scoreData.implications.slice(0, 3).map(impl => `<li class="flex items-start"><span class="text-blue-500 mr-1">•</span>${impl}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }).join('');
}


