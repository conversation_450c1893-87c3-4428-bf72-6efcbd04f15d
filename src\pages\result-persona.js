export function createResultPersonaPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50">
      <!-- Futuristic Navigation -->
      <nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/80 border-b border-gray-200/60">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-8">
              <button onclick="navigateTo('result-ocean')"
                class="group flex items-center text-gray-500 hover:text-gray-900 transition-all duration-300">
                <div class="w-8 h-8 mr-3 rounded-full bg-gray-100 group-hover:bg-gray-200 flex items-center justify-center transition-all duration-300">
                  <svg class="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </div>
                <span class="font-medium tracking-wide">OCEAN</span>
              </button>
              <div class="h-4 w-px bg-gray-300"></div>
              <h1 class="text-lg font-semibold text-gray-900 tracking-wide">Persona Analysis</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3 px-4 py-2 rounded-full bg-purple-50 border border-purple-100">
                <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-purple-700">Complete</span>
              </div>
              <button onclick="navigateTo('result')" 
                class="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                View Summary
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Progress Indicator -->
      <div class="pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-6">
          <div class="relative">
            <div class="h-1 bg-gradient-to-r from-gray-200 via-gray-200 to-gray-200 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-emerald-400 via-blue-500 to-purple-600 rounded-full transition-all duration-1000 ease-out" style="width: 100%"></div>
            </div>
            <div class="absolute -top-2 right-0 transform translate-x-2">
              <div class="w-5 h-5 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto px-6 pb-12">
        <!-- Hero Profile Card -->
        <div class="relative mb-12">
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl p-10 border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-indigo-500/5 rounded-3xl"></div>
            <div class="relative z-10">
              <div class="flex items-start space-x-8">
                <div class="flex-shrink-0">
                  <div class="w-28 h-28 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span class="text-5xl relative z-10">🧠</span>
                  </div>
                </div>
                <div class="flex-1">
                  <h1 class="text-4xl font-bold text-gray-900 mb-4 tracking-tight" id="archetype">The Analytical Innovator</h1>
                  <p class="text-gray-600 text-lg leading-relaxed mb-8 max-w-3xl" id="short-summary">
                    You are an analytical thinker with strong investigative tendencies and high creativity,
                    combining systematic thinking with innovative problem-solving approaches.
                  </p>
                  <div class="flex flex-wrap gap-4">
                    <div class="px-5 py-2.5 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-full border border-purple-100">
                      <span class="text-sm font-medium text-purple-700">
                        Risk Tolerance: <span id="risk-tolerance" class="font-semibold">Moderate</span>
                      </span>
                    </div>
                    <div class="px-5 py-2.5 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-full border border-blue-100">
                      <span class="text-sm font-medium text-blue-700">
                        Leadership: <span class="font-semibold">Collaborative</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Core Analysis Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Strengths -->
          <div class="group">
            <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-500 h-full">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">Core Strengths</h3>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed" id="strength-summary">
                Your primary strengths lie in deep analysis, creativity, and a strong drive to learn new things and solve complex problems.
              </p>
              <div class="space-y-4" id="strengths-list">
                <!-- Strengths will be populated by JavaScript -->
              </div>
            </div>
          </div>

          <!-- Development Areas -->
          <div class="group">
            <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-500 h-full">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">Growth Areas</h3>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed" id="weakness-summary">
                Areas for development include managing perfectionism and enhancing collaborative communication skills.
              </p>
              <div class="space-y-4" id="weaknesses-list">
                <!-- Weaknesses will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Career Recommendations -->
        <div class="mb-12">
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-8">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900">Career Pathways</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="career-recommendations">
              <!-- Career recommendations will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Insights & Skills Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Work Environment -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900">Ideal Environment</h3>
            </div>
            <p class="text-gray-600 leading-relaxed" id="work-environment">
              Environment that provides intellectual autonomy, values innovation, and offers continuous cognitive challenges.
            </p>
          </div>

          <!-- Key Insights -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900">Key Insights</h3>
            </div>
            <div class="space-y-3" id="insights-list">
              <!-- Insights will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Skills & Development -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Recommended Skills -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900">Skill Development</h3>
            </div>
            <div class="flex flex-wrap gap-3" id="skill-suggestions">
              <!-- Skills will be populated by JavaScript -->
            </div>
          </div>

          <!-- Potential Challenges -->
          <div class="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-8 border border-red-100 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-red-800">Potential Pitfalls</h3>
            </div>
            <div class="space-y-4" id="pitfalls-list">
              <!-- Pitfalls will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Role Models -->
        <div class="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-2xl p-8 border border-amber-100 shadow-lg mb-12">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-amber-800">Inspiring Role Models</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="role-models">
            <!-- Role models will be populated by JavaScript -->
          </div>
        </div>

        <!-- Assessment Summary -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Assessment Overview</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="group cursor-pointer" onclick="navigateTo('result-via-is')">
              <div class="bg-gradient-to-br from-purple-50 to-indigo-50 p-6 rounded-xl border border-purple-100 hover:border-purple-200 transition-all duration-300 hover:shadow-lg">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                    <span class="text-2xl">💎</span>
                  </div>
                  <h4 class="font-semibold text-purple-800 mb-1">VIA-IS</h4>
                  <p class="text-purple-600 text-sm mb-3">Character Strengths</p>
                  <div class="inline-flex items-center text-purple-600 hover:text-purple-800 text-sm font-medium group-hover:translate-x-1 transition-transform duration-300">
                    View Details
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="group cursor-pointer" onclick="navigateTo('result-riasec')">
              <div class="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-xl border border-blue-100 hover:border-blue-200 transition-all duration-300 hover:shadow-lg">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                    <span class="text-2xl">🎯</span>
                  </div>
                  <h4 class="font-semibold text-blue-800 mb-1">RIASEC</h4>
                  <p class="text-blue-600 text-sm mb-3">Holland Codes</p>
                  <div class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium group-hover:translate-x-1 transition-transform duration-300">
                    View Details
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="group cursor-pointer" onclick="navigateTo('result-ocean')">
              <div class="bg-gradient-to-br from-emerald-50 to-teal-50 p-6 rounded-xl border border-emerald-100 hover:border-emerald-200 transition-all duration-300 hover:shadow-lg">
                <div class="text-center">
                  <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                    <span class="text-2xl">🌊</span>
                  </div>
                  <h4 class="font-semibold text-emerald-800 mb-1">OCEAN</h4>
                  <p class="text-emerald-600 text-sm mb-3">Big Five Personality</p>
                  <div class="inline-flex items-center text-emerald-600 hover:text-emerald-800 text-sm font-medium group-hover:translate-x-1 transition-transform duration-300">
                    View Details
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Center -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button onclick="retakeAssessment()" 
              class="group flex items-center justify-center py-4 px-6 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-300">
              <svg class="w-5 h-5 mr-3 group-hover:rotate-180 transition-transform duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              <span class="font-medium">Retake Assessment</span>
            </button>
            <button onclick="scheduleConsultation()" 
              class="group flex items-center justify-center py-4 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
              <span class="font-medium">Book Consultation</span>
            </button>
            <button onclick="downloadResult()" 
              class="group flex items-center justify-center py-4 px-6 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              <svg class="w-5 h-5 mr-3 group-hover:translate-y-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <span class="font-medium">Download PDF</span>
            </button>
          </div>
        </div>

        <!-- Final Navigation -->
        <div class="flex justify-between items-center">
          <button onclick="navigateTo('result-ocean')"
            class="group flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-300 border border-gray-200 hover:border-gray-300">
            <svg class="w-5 h-5 mr-3 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to OCEAN</span>
          </button>
          <button onclick="navigateTo('result')"
            class="group flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <span class="font-medium mr-3">View Complete Summary</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  `;
}

// Initialize the persona result page
export function initResultPersona() {
  loadPersonaData();
}

function loadPersonaData() {
  try {
    // Load persona profile data
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : getSamplePersonaProfile();

    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleAssessmentResults();

    console.log('Persona data loaded:', { profile, assessmentResults });

    displayPersonaData(profile, assessmentResults);
  } catch (error) {
    console.error('Error loading persona data:', error);
    // Fallback to sample data
    displayPersonaData(getSamplePersonaProfile(), getSampleAssessmentResults());
  }
}

function getSamplePersonaProfile() {
  return {
    archetype: "The Analytical Innovator",
    shortSummary: "You are an analytical thinker with strong investigative tendencies and high creativity, combining systematic thinking with innovative problem-solving approaches.",
    strengthSummary: "Your primary strengths lie in deep analysis, creativity, and a strong drive to learn new things and solve complex problems.",
    weaknessSummary: "Areas for development include managing perfectionism and enhancing collaborative communication skills.",
    riskTolerance: "Moderate",
    workEnvironment: "Environment that provides intellectual autonomy, values innovation, and offers continuous cognitive challenges.",
    strengths: [
      "Deep analytical thinking and problem-solving",
      "High creativity and innovative approaches",
      "Strong intellectual curiosity and love of learning",
      "Systematic and methodical work style",
      "Independent and self-motivated"
    ],
    weaknesses: [
      "Tendency toward perfectionism",
      "May struggle with routine tasks",
      "Can be overly critical of ideas",
      "Sometimes prefers working alone",
      "May overthink simple decisions"
    ],
    careerRecommendations: [
      {
        title: "Research Scientist",
        description: "Conduct innovative research in your field of interest",
        match: "95%"
      },
      {
        title: "Data Analyst",
        description: "Analyze complex data to derive meaningful insights",
        match: "90%"
      },
      {
        title: "Product Manager",
        description: "Lead product development with analytical approach",
        match: "85%"
      }
    ],
    insights: [
      "You thrive in environments that challenge your intellect",
      "Innovation and creativity are your key drivers",
      "You prefer autonomy in your work approach",
      "Continuous learning is essential for your satisfaction"
    ],
    skillSuggestions: [
      "Data Analysis", "Research Methods", "Critical Thinking",
      "Innovation Management", "Strategic Planning", "Problem Solving"
    ],
    pitfalls: [
      {
        title: "Analysis Paralysis",
        description: "Tendency to over-analyze decisions, leading to delays"
      },
      {
        title: "Perfectionism",
        description: "Setting unrealistic standards that can hinder progress"
      },
      {
        title: "Communication Gaps",
        description: "May struggle to explain complex ideas to others"
      }
    ],
    roleModels: [
      {
        name: "Marie Curie",
        description: "Pioneering scientist and researcher",
        reason: "Analytical thinking and groundbreaking innovation"
      },
      {
        name: "Elon Musk",
        description: "Entrepreneur and innovator",
        reason: "Systematic approach to complex problems"
      },
      {
        name: "Ada Lovelace",
        description: "First computer programmer",
        reason: "Mathematical thinking and creative problem-solving"
      }
    ]
  };
}

function getSampleAssessmentResults() {
  return {
    ocean: {
      openness: 85,
      conscientiousness: 78,
      extraversion: 45,
      agreeableness: 62,
      neuroticism: 35
    },
    riasec: {
      realistic: 25,
      investigative: 92,
      artistic: 78,
      social: 45,
      enterprising: 55,
      conventional: 68
    },
    viaIs: {
      creativity: 88,
      curiosity: 92,
      judgment: 85,
      loveOfLearning: 90,
      perspective: 82,
      bravery: 65,
      perseverance: 78,
      honesty: 75,
      zest: 58,
      love: 62,
      kindness: 68,
      socialIntelligence: 55,
      teamwork: 48,
      fairness: 72,
      leadership: 65,
      forgiveness: 58,
      humility: 62,
      prudence: 85,
      selfRegulation: 75,
      appreciationOfBeauty: 82,
      gratitude: 68,
      hope: 72,
      humor: 55,
      spirituality: 45
    }
  };
}

function displayPersonaData(profile, assessmentResults) {
  // Update main profile information
  updateProfileHeader(profile);

  // Display strengths and weaknesses
  displayStrengthsAndWeaknesses(profile);

  // Display career recommendations
  displayCareerRecommendations(profile.careerRecommendations);

  // Display work environment and insights
  displayEnvironmentAndInsights(profile);

  // Display skills and pitfalls
  displaySkillsAndPitfalls(profile);

  // Display role models
  displayRoleModels(profile.roleModels);
}

function updateProfileHeader(profile) {
  // Update archetype
  const archetypeElement = document.getElementById('archetype');
  if (archetypeElement) {
    archetypeElement.textContent = profile.archetype;
  }

  // Update short summary
  const shortSummaryElement = document.getElementById('short-summary');
  if (shortSummaryElement) {
    shortSummaryElement.textContent = profile.shortSummary;
  }

  // Update risk tolerance
  const riskToleranceElement = document.getElementById('risk-tolerance');
  if (riskToleranceElement) {
    riskToleranceElement.textContent = profile.riskTolerance;
  }
}

function displayStrengthsAndWeaknesses(profile) {
  // Display strength summary
  const strengthSummaryElement = document.getElementById('strength-summary');
  if (strengthSummaryElement) {
    strengthSummaryElement.textContent = profile.strengthSummary;
  }

  // Display strengths list
  const strengthsListElement = document.getElementById('strengths-list');
  if (strengthsListElement && profile.strengths) {
    strengthsListElement.innerHTML = profile.strengths.map(strength => `
      <div class="flex items-start space-x-3">
        <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
        <span class="text-gray-700 leading-relaxed">${strength}</span>
      </div>
    `).join('');
  }

  // Display weakness summary
  const weaknessSummaryElement = document.getElementById('weakness-summary');
  if (weaknessSummaryElement) {
    weaknessSummaryElement.textContent = profile.weaknessSummary;
  }

  // Display weaknesses list
  const weaknessesListElement = document.getElementById('weaknesses-list');
  if (weaknessesListElement && profile.weaknesses) {
    weaknessesListElement.innerHTML = profile.weaknesses.map(weakness => `
      <div class="flex items-start space-x-3">
        <div class="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
        <span class="text-gray-700 leading-relaxed">${weakness}</span>
      </div>
    `).join('');
  }
}

function displayCareerRecommendations(careerRecommendations) {
  const container = document.getElementById('career-recommendations');
  if (!container || !careerRecommendations) return;

  container.innerHTML = careerRecommendations.map(career => `
    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:border-blue-200 transition-all duration-300 hover:shadow-lg">
      <div class="flex justify-between items-start mb-4">
        <h4 class="font-semibold text-lg text-blue-900">${career.title}</h4>
        <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">${career.match}</span>
      </div>
      <p class="text-blue-700 text-sm leading-relaxed">${career.description}</p>
    </div>
  `).join('');
}

function displayEnvironmentAndInsights(profile) {
  // Update work environment
  const workEnvironmentElement = document.getElementById('work-environment');
  if (workEnvironmentElement) {
    workEnvironmentElement.textContent = profile.workEnvironment;
  }

  // Display insights
  const insightsListElement = document.getElementById('insights-list');
  if (insightsListElement && profile.insights) {
    insightsListElement.innerHTML = profile.insights.map(insight => `
      <div class="flex items-start space-x-3">
        <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
        <span class="text-gray-700 text-sm leading-relaxed">${insight}</span>
      </div>
    `).join('');
  }
}

function displaySkillsAndPitfalls(profile) {
  // Display skill suggestions
  const skillSuggestionsElement = document.getElementById('skill-suggestions');
  if (skillSuggestionsElement && profile.skillSuggestions) {
    skillSuggestionsElement.innerHTML = profile.skillSuggestions.map(skill => `
      <span class="px-4 py-2 bg-gradient-to-r from-indigo-50 to-purple-50 text-indigo-700 rounded-full text-sm font-medium border border-indigo-100 hover:border-indigo-200 transition-colors duration-300">
        ${skill}
      </span>
    `).join('');
  }

  // Display pitfalls
  const pitfallsListElement = document.getElementById('pitfalls-list');
  if (pitfallsListElement && profile.pitfalls) {
    pitfallsListElement.innerHTML = profile.pitfalls.map(pitfall => `
      <div class="flex items-start space-x-3">
        <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <svg class="w-3 h-3 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div>
          <h5 class="font-medium text-red-800 mb-1">${pitfall.title}</h5>
          <p class="text-red-700 text-sm leading-relaxed">${pitfall.description}</p>
        </div>
      </div>
    `).join('');
  }
}

function displayRoleModels(roleModels) {
  const container = document.getElementById('role-models');
  if (!container || !roleModels) return;

  container.innerHTML = roleModels.map(model => `
    <div class="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl p-6 border border-amber-100 hover:border-amber-200 transition-all duration-300 hover:shadow-lg">
      <h4 class="font-semibold text-amber-900 mb-2">${model.name}</h4>
      <p class="text-amber-700 text-sm mb-3">${model.description}</p>
      <div class="text-xs text-amber-600">
        <strong>Why inspiring:</strong> ${model.reason}
      </div>
    </div>
  `).join('');
}

// Action functions
export function retakeAssessment() {
  if (confirm('Are you sure you want to retake the assessment? This will clear your current results.')) {
    // Clear stored data
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('personaProfile');
    localStorage.removeItem('assessment3PhaseAnswers');

    // Navigate to assessment
    window.location.href = '#assessment-3phase';
  }
}

export function scheduleConsultation() {
  alert('Consultation scheduling feature coming soon! Please contact us directly for now.');
}

export function downloadResult() {
  alert('PDF download feature coming soon! You can take a screenshot for now.');
}