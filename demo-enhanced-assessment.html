<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Enhanced Assessment Results</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <div class="max-w-6xl mx-auto py-8 px-4">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Enhanced Assessment Results Demo</h1>
        
        <!-- Assessment Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Hasil Assessment Psikometri</h2>
            
            <!-- Assessment Explanations -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                        <span class="text-blue-500 mr-2">🎯</span>
                        RIASEC
                    </h4>
                    <p class="text-blue-700 text-sm mb-2">Holland Codes - Minat Karir</p>
                    <p class="text-blue-600 text-xs leading-relaxed">
                        Mengukur 6 tipe kepribadian kerja: Realistic (praktis), Investigative (analitis),
                        Artistic (kreatif), Social (sosial), Enterprising (wirausaha), Conventional (terorganisir).
                    </p>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="font-semibold text-green-800 mb-2 flex items-center">
                        <span class="text-green-500 mr-2">🌊</span>
                        OCEAN
                    </h4>
                    <p class="text-green-700 text-sm mb-2">Big Five Personality</p>
                    <p class="text-green-600 text-xs leading-relaxed">
                        Mengukur 5 dimensi kepribadian: Openness (keterbukaan), Conscientiousness (kehati-hatian),
                        Extraversion (ekstraversi), Agreeableness (keramahan), Neuroticism (neurotisisme).
                    </p>
                </div>

                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 class="font-semibold text-purple-800 mb-2 flex items-center">
                        <span class="text-purple-500 mr-2">💎</span>
                        VIA-IS
                    </h4>
                    <p class="text-purple-700 text-sm mb-2">Character Strengths</p>
                    <p class="text-purple-600 text-xs leading-relaxed">
                        Mengidentifikasi 24 kekuatan karakter dalam 6 kategori: Wisdom, Courage, Humanity,
                        Justice, Temperance, dan Transcendence untuk pengembangan diri optimal.
                    </p>
                </div>
            </div>

            <!-- Radar Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div>
                    <h4 class="font-medium text-gray-900 mb-4">Profil Kepribadian (OCEAN)</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <canvas id="oceanRadarChart" width="300" height="300"></canvas>
                    </div>
                </div>

                <div>
                    <h4 class="font-medium text-gray-900 mb-4">Minat Karir (RIASEC)</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <canvas id="riasecRadarChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- VIA-IS Visualizations -->
            <div class="mt-8">
                <h4 class="font-medium text-gray-900 mb-6">VIA-IS Character Strengths Visualization</h4>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-700 mb-4">Categories Overview</h5>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <canvas id="viaIsCategoriesRadarChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-700 mb-4">Top Character Strengths</h5>
                        <div class="bg-gray-50 rounded-lg p-4" style="height: 300px; overflow-y: auto;">
                            <canvas id="viaIsStrengthsBarChart" width="300" height="600"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample RIASEC Detail -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span class="text-blue-500 mr-2">🎯</span>
                Detail RIASEC Holland Codes
            </h3>
            <div class="space-y-4">
                <div class="border rounded-lg p-4 border-blue-300 bg-blue-50">
                    <div class="flex justify-between items-start mb-3">
                        <h5 class="font-semibold text-gray-900">Investigative (I) - The Thinkers</h5>
                        <div class="text-right">
                            <div class="text-lg font-bold text-blue-600">85%</div>
                            <div class="text-xs text-blue-500">Tinggi</div>
                        </div>
                    </div>
                    <p class="text-gray-700 text-sm mb-3">Orang dengan tipe Investigative menyukai aktivitas yang melibatkan penelitian, analisis, dan pemecahan masalah kompleks.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h6 class="font-medium text-gray-800 mb-2">Karakteristik:</h6>
                            <ul class="text-gray-600 space-y-1">
                                <li>• Analitis dan logis dalam berpikir</li>
                                <li>• Menyukai penelitian dan eksplorasi</li>
                                <li>• Tertarik pada teori dan konsep abstrak</li>
                            </ul>
                        </div>
                        <div>
                            <h6 class="font-medium text-gray-800 mb-2">Contoh Karir:</h6>
                            <ul class="text-gray-600 space-y-1">
                                <li>• Peneliti</li>
                                <li>• Ilmuwan</li>
                                <li>• Analis Data</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample VIA-IS Category -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span class="text-purple-500 mr-2">💎</span>
                VIA-IS Character Strengths Categories
            </h3>
            <div class="border rounded-lg p-4 bg-blue-50 border-blue-200 text-blue-800">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">🧠</span>
                        <div>
                            <h5 class="font-semibold text-lg">Wisdom & Knowledge</h5>
                            <p class="text-sm opacity-80">Kekuatan kognitif yang melibatkan akuisisi dan penggunaan pengetahuan</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold">82%</div>
                        <div class="text-xs">Rata-rata</div>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="w-full bg-white bg-opacity-50 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: 82%"></div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    <div class="bg-white bg-opacity-70 rounded p-3">
                        <div class="flex justify-between items-center mb-1">
                            <span class="font-medium text-sm">Kreativitas</span>
                            <span class="text-sm font-bold">85%</span>
                        </div>
                        <p class="text-xs opacity-80">Kemampuan menghasilkan ide-ide baru dan original</p>
                    </div>
                    <div class="bg-white bg-opacity-70 rounded p-3">
                        <div class="flex justify-between items-center mb-1">
                            <span class="font-medium text-sm">Keingintahuan</span>
                            <span class="text-sm font-bold">88%</span>
                        </div>
                        <p class="text-xs opacity-80">Minat yang kuat untuk terus belajar dan mengeksplorasi</p>
                    </div>
                    <div class="bg-white bg-opacity-70 rounded p-3">
                        <div class="flex justify-between items-center mb-1">
                            <span class="font-medium text-sm">Cinta Belajar</span>
                            <span class="text-sm font-bold">90%</span>
                        </div>
                        <p class="text-xs opacity-80">Passion untuk terus mengembangkan pengetahuan dan keterampilan</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data for demo
        const sampleData = {
            ocean: {
                openness: 85,
                conscientiousness: 72,
                extraversion: 45,
                agreeableness: 68,
                neuroticism: 35
            },
            riasec: {
                realistic: 25,
                investigative: 85,
                artistic: 78,
                social: 55,
                enterprising: 42,
                conventional: 38
            },
            viaIs: {
                creativity: 85, curiosity: 88, judgment: 75, loveOfLearning: 90, perspective: 70,
                bravery: 65, perseverance: 80, honesty: 85, zest: 72,
                love: 68, kindness: 75, socialIntelligence: 62,
                teamwork: 58, fairness: 72, leadership: 55,
                forgiveness: 70, humility: 68, prudence: 65, selfRegulation: 60,
                appreciationOfBeauty: 82, gratitude: 78, hope: 75, humor: 70, spirituality: 65
            }
        };

        // Create charts
        createOceanChart();
        createRiasecChart();
        createViaIsCategoriesChart();
        createViaIsStrengthsChart();

        function createOceanChart() {
            const ctx = document.getElementById('oceanRadarChart');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Openness', 'Conscientiousness', 'Extraversion', 'Agreeableness', 'Neuroticism'],
                    datasets: [{
                        data: [sampleData.ocean.openness, sampleData.ocean.conscientiousness, 
                               sampleData.ocean.extraversion, sampleData.ocean.agreeableness, 
                               sampleData.ocean.neuroticism],
                        fill: true,
                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        borderColor: 'rgb(34, 197, 94)',
                        pointBackgroundColor: 'rgb(34, 197, 94)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { r: { beginAtZero: true, max: 100 } }
                }
            });
        }

        function createRiasecChart() {
            const ctx = document.getElementById('riasecRadarChart');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Realistic', 'Investigative', 'Artistic', 'Social', 'Enterprising', 'Conventional'],
                    datasets: [{
                        data: [sampleData.riasec.realistic, sampleData.riasec.investigative,
                               sampleData.riasec.artistic, sampleData.riasec.social,
                               sampleData.riasec.enterprising, sampleData.riasec.conventional],
                        fill: true,
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgb(59, 130, 246)',
                        pointBackgroundColor: 'rgb(59, 130, 246)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { r: { beginAtZero: true, max: 100 } }
                }
            });
        }

        function createViaIsCategoriesChart() {
            const ctx = document.getElementById('viaIsCategoriesRadarChart');
            const categoryAverages = [82, 75, 68, 62, 63, 74]; // Sample averages
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Wisdom', 'Courage', 'Humanity', 'Justice', 'Temperance', 'Transcendence'],
                    datasets: [{
                        data: categoryAverages,
                        fill: true,
                        backgroundColor: 'rgba(168, 85, 247, 0.2)',
                        borderColor: 'rgb(168, 85, 247)',
                        pointBackgroundColor: 'rgb(168, 85, 247)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { r: { beginAtZero: true, max: 100 } }
                }
            });
        }

        function createViaIsStrengthsChart() {
            const ctx = document.getElementById('viaIsStrengthsBarChart');
            const strengths = Object.entries(sampleData.viaIs)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // Top 10 for demo
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: strengths.map(s => s[0]),
                    datasets: [{
                        data: strengths.map(s => s[1]),
                        backgroundColor: strengths.map((_, i) => `hsla(${270 - i * 15}, 70%, 60%, 0.8)`),
                        borderColor: strengths.map((_, i) => `hsla(${270 - i * 15}, 70%, 50%, 1)`),
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { beginAtZero: true, max: 100 } }
                }
            });
        }
    </script>
</body>
</html>
