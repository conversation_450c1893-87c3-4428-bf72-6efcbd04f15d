<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Result Persona Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <h1>Test Result Persona Page</h1>
    <p>Click the button below to save test data and navigate to the result-persona page.</p>
    
    <button onclick="saveTestData()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
        Save Test Data & Open Result Persona Page
    </button>
    <button onclick="clearData()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 ml-2">
        Clear Data
    </button>
    
    <script>
        const personaProfileExample = {
            archetype: "The Analytical Innovator",
            shortSummary: "You are an analytical thinker with strong investigative tendencies and high creativity, combining systematic thinking with innovative problem-solving approaches.",
            strengthSummary: "Your primary strengths lie in deep analysis, creativity, and a strong drive to learn new things and solve complex problems.",
            weaknessSummary: "Areas for development include managing perfectionism and enhancing collaborative communication skills.",
            riskTolerance: "Moderate",
            workEnvironment: "Environment that provides intellectual autonomy, values innovation, and offers continuous cognitive challenges.",
            strengths: [
                "Deep analytical thinking and problem-solving",
                "High creativity and innovative approaches",
                "Strong intellectual curiosity and love of learning",
                "Systematic and methodical work style",
                "Independent and self-motivated"
            ],
            weaknesses: [
                "Tendency toward perfectionism",
                "May struggle with routine tasks",
                "Can be overly critical of ideas",
                "Sometimes prefers working alone",
                "May overthink simple decisions"
            ],
            careerRecommendations: [
                {
                    title: "Research Scientist",
                    description: "Conduct innovative research in your field of interest",
                    match: "95%"
                },
                {
                    title: "Data Analyst",
                    description: "Analyze complex data to derive meaningful insights",
                    match: "90%"
                },
                {
                    title: "Product Manager",
                    description: "Lead product development with analytical approach",
                    match: "85%"
                }
            ],
            insights: [
                "You thrive in environments that challenge your intellect",
                "Innovation and creativity are your key drivers",
                "You prefer autonomy in your work approach",
                "Continuous learning is essential for your satisfaction"
            ],
            skillSuggestions: [
                "Data Analysis", "Research Methods", "Critical Thinking", 
                "Innovation Management", "Strategic Planning", "Problem Solving"
            ],
            pitfalls: [
                {
                    title: "Analysis Paralysis",
                    description: "Tendency to over-analyze decisions, leading to delays"
                },
                {
                    title: "Perfectionism",
                    description: "Setting unrealistic standards that can hinder progress"
                },
                {
                    title: "Communication Gaps",
                    description: "May struggle to explain complex ideas to others"
                }
            ],
            roleModels: [
                {
                    name: "Marie Curie",
                    description: "Pioneering scientist and researcher",
                    reason: "Analytical thinking and groundbreaking innovation"
                },
                {
                    name: "Elon Musk",
                    description: "Entrepreneur and innovator",
                    reason: "Systematic approach to complex problems"
                },
                {
                    name: "Ada Lovelace",
                    description: "First computer programmer",
                    reason: "Mathematical thinking and creative problem-solving"
                }
            ]
        };

        // Sample assessment results data
        const sampleAssessmentResults = {
            ocean: {
                openness: 85,
                conscientiousness: 78,
                extraversion: 45,
                agreeableness: 62,
                neuroticism: 35
            },
            riasec: {
                realistic: 25,
                investigative: 92,
                artistic: 78,
                social: 45,
                enterprising: 55,
                conventional: 68
            },
            viaIs: {
                creativity: 88,
                curiosity: 92,
                judgment: 85,
                loveOfLearning: 90,
                perspective: 82,
                bravery: 65,
                perseverance: 78,
                honesty: 75,
                zest: 58,
                love: 62,
                kindness: 68,
                socialIntelligence: 55,
                teamwork: 48,
                fairness: 72,
                leadership: 65,
                forgiveness: 58,
                humility: 62,
                prudence: 85,
                selfRegulation: 75,
                appreciationOfBeauty: 82,
                gratitude: 68,
                hope: 72,
                humor: 55,
                spirituality: 45
            }
        };

        function saveTestData() {
            // Save persona profile
            localStorage.setItem('personaProfile', JSON.stringify(personaProfileExample));

            // Save assessment results
            localStorage.setItem('assessmentResults', JSON.stringify(sampleAssessmentResults));

            // Set authentication data
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('userToken', 'dummy-token');
            localStorage.setItem('userData', JSON.stringify({
                email: '<EMAIL>',
                name: 'Test User'
            }));

            alert('Test data saved successfully!');

            // Navigate to result-persona page
            window.location.href = 'http://localhost:5173/#result-persona';
        }

        function clearData() {
            localStorage.removeItem('personaProfile');
            localStorage.removeItem('assessmentResults');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            alert('Data cleared!');
        }
    </script>
</body>
</html>
